2025-07-16 00:27:22,020 - modules.auth.utils.error_handler - ERROR - error_handler.py:39 - handle_redis_error - Redis错误 - 操作: test_operation, 错误: 测试Redis连接错误2025-07-18 02:08:44,795 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒2025-07-21 09:40:27,545 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-21 09:40:27,545 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-21 09:40:58,837 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-07-21 09:41:14,274 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin, IP: 127.0.0.1
2025-07-21 09:41:14,875 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: pheud4Cw..., 用户ID: 1, 过期时间: 300秒, 结果: 成功
2025-07-21 09:41:14,875 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 admin 生成2FA临时token
2025-07-21 09:41:38,369 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=pheud4Cw...
2025-07-21 09:41:40,069 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: admin, IP: 127.0.0.1
2025-07-21 09:41:40,073 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: pheud4Cw...
2025-07-21 09:41:40,743 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=Af6RDYCB75zOq5vQc6UNc_Z92GYUfmhXYdUTVRsZYe8
2025-07-21 09:41:40,744 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: admin
2025-07-22 14:38:55,844 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 14:38:55,844 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 14:40:04,301 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin, IP: 127.0.0.1
2025-07-22 14:40:05,226 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: oJba9jd0..., 用户ID: 1, 过期时间: 300秒, 结果: 成功
2025-07-22 14:40:40,635 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=oJba9jd0...
2025-07-22 14:40:40,971 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=oJba9jd0...
2025-07-22 14:40:40,977 - modules.auth.services.totp_service - INFO - totp_service.py:204 - verify_and_enable_2fa - 用户 1 完成2FA首次验证
2025-07-22 14:40:40,978 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: oJba9jd0...
2025-07-22 14:40:40,979 - modules.auth.services.totp_service - ERROR - totp_service.py:209 - verify_and_enable_2fa - 验证并启用2FA失败: TransactionContext Error: Conflict on update!
2025-07-22 14:40:41,832 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=6HvXZ3EPEBvdDvGeM4-_DjckyaYyaI34jMj9b_O9ePQ
2025-07-22 14:40:41,832 - modules.auth.api.twofa_api - INFO - twofa_api.py:149 - setup_verify_2fa - 用户2FA设置成功并完成登录: admin
2025-07-22 14:40:56,165 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 new_user
2025-07-22 14:41:02,810 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 test_user2
2025-07-22 14:43:26,209 - modules.auth.services.totp_service - INFO - totp_service.py:172 - setup_2fa_for_user - 为用户 reveen 设置2FA
2025-07-22 14:43:26,209 - modules.auth.services.auth_service - INFO - auth_service.py:141 - create_user - 创建用户成功: reveen, 角色: admin, 已强制设置2FA
2025-07-22 15:23:31,268 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:23:31,268 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:23:57,548 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:23:57,548 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:26:30,182 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:26:30,182 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:27:00,416 - modules.auth.services.session_manager - INFO - session_manager.py:79 - destroy_session - 销毁会话: 6HvXZ3EPEBvdDvGeM4-_DjckyaYyaI34jMj9b_O9ePQ
2025-07-22 15:27:01,107 - modules.auth.api.auth_api - INFO - auth_api.py:295 - logout - 用户登出: admin, IP: 127.0.0.1
2025-07-22 15:33:39,101 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:33:39,101 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:33:48,657 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:33:48,657 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:34:41,779 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:34:41,779 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:38:47,240 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:38:47,240 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:41:16,470 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: reveen, IP: 127.0.0.1
2025-07-22 15:41:17,131 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: TH868waq..., 用户ID: 2, 过期时间: 300秒, 结果: 成功
2025-07-22 15:43:28,951 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:43:28,951 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 15:45:59,747 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 15:45:59,747 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:01:11,397 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 16:01:11,397 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:04:26,765 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 16:04:26,766 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:04:31,848 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-22 16:04:31,849 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-22 16:04:44,791 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: reveen, IP: 127.0.0.1
2025-07-22 16:04:45,126 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: beEjnXCA..., 用户ID: 2, 过期时间: 300秒, 结果: 成功
2025-07-22 16:04:54,597 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin, IP: 127.0.0.1
2025-07-22 16:04:54,803 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: Qi7XsPec..., 用户ID: 1, 过期时间: 300秒, 结果: 成功
2025-07-22 16:04:54,803 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 admin 生成2FA临时token
2025-07-22 16:05:14,373 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=Qi7XsPec...
2025-07-22 16:05:14,688 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=1, token=Qi7XsPec...
2025-07-22 16:05:14,739 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: admin, IP: 127.0.0.1
2025-07-22 16:05:14,739 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: Qi7XsPec...
2025-07-22 16:05:14,922 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: admin, IP: 127.0.0.1
2025-07-22 16:05:14,923 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:150 - invalidate_temp_token - 临时token失效失败: Qi7XsPec...
2025-07-22 16:05:14,924 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=a2j39K5OF6lEQGbgfONWflOHjkQFmUx2nT16B1qPJ5o
2025-07-22 16:05:14,924 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: admin
2025-07-22 16:05:15,038 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=1, session_id=UkpD6qKZeETrOws164MxWvB_ZXxWJnW-q3YIP4MOo7s
2025-07-22 16:05:15,039 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: admin
2025-07-22 16:05:50,941 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 retry_user_4
2025-07-22 16:05:54,569 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 retry_user_0
2025-07-22 16:05:58,320 - modules.auth.api.user_api - INFO - user_api.py:250 - delete_user - 管理员 admin 删除了用户 totp_test_user
2025-07-23 09:20:02,672 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-07-23 09:20:02,672 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-07-23 09:20:09,223 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: admin, IP: 127.0.0.1, 原因: 用户不存在
2025-07-23 09:20:24,019 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-07-23 09:20:24,053 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: vo84nUcV..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-07-23 09:20:45,042 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=vo84nUcV...
2025-07-23 09:20:45,061 - modules.auth.services.totp_service - INFO - totp_service.py:215 - verify_and_enable_2fa - 用户 817184 完成2FA首次验证
2025-07-23 09:20:45,066 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: vo84nUcV...
2025-07-23 09:20:45,090 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=xE2PtInnXeCzz2TiJUq1-r4JQaBu9BJdax9Vzbqaxus
2025-07-23 09:20:45,090 - modules.auth.api.twofa_api - INFO - twofa_api.py:149 - setup_verify_2fa - 用户2FA设置成功并完成登录: bearyang
2025-08-01 23:15:23,294 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-01 23:15:23,294 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-01 23:15:55,337 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: K7a2QkDo...
2025-08-01 23:16:04,307 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: K7a2QkDo...
2025-08-01 23:16:14,533 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: K7a2QkDo...
2025-08-01 23:16:36,060 - modules.auth.services.temp_token_service - WARNING - temp_token_service.py:102 - verify_temp_token - 临时token无效或已过期: K7a2QkDo...
2025-08-01 23:16:48,075 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-01 23:16:48,244 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: yk9SA7So..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-01 23:16:48,244 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-01 23:16:57,401 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=yk9SA7So...
2025-08-01 23:16:57,846 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-01 23:16:57,847 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: yk9SA7So...
2025-08-01 23:16:58,060 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=-m-5z4VXoRosnXqRRD9qvibzo3-L9e-TPw4qhybStfk
2025-08-01 23:16:58,060 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-02 00:18:06,254 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 00:18:06,254 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 00:26:51,285 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 00:26:51,285 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 00:34:09,286 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 00:34:09,286 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 00:39:44,985 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 00:39:44,985 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:24:06,130 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:24:06,130 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:37:29,108 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:37:29,108 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:39:20,720 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:39:20,720 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:39:24,793 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:39:24,793 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:41:05,131 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:41:05,131 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:51:55,336 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:51:55,336 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:52:11,294 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:52:11,294 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:56:36,261 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:56:36,261 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:57:35,814 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:57:35,814 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 01:59:44,803 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 01:59:44,803 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:00:00,296 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:00:00,296 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:00:02,396 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:00:02,396 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:00:27,655 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:00:27,655 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:01:53,364 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:01:53,364 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:02:12,813 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:02:12,813 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:04:46,124 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:04:46,124 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:05:19,410 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:05:19,410 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:06:32,809 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:06:32,809 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:06:49,100 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:06:49,101 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:08:12,274 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:08:12,274 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:20:40,756 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:20:40,756 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 02:20:55,389 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 02:20:55,389 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 16:03:49,278 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 16:03:49,278 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 17:47:40,039 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 17:47:40,039 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 20:01:23,520 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 20:01:23,520 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 23:13:50,779 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 23:13:50,779 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 23:31:42,155 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-02 23:31:42,155 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-02 23:31:55,916 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-02 23:31:56,154 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: AZSyIRLH..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-02 23:31:56,154 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-02 23:32:12,194 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=AZSyIRLH...
2025-08-02 23:32:12,799 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-02 23:32:12,800 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: AZSyIRLH...
2025-08-02 23:32:13,073 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=WtVepILr-Ycd9UkXc2JcY2PbBuep5A8x-rSJ7AW8wcA
2025-08-02 23:32:13,073 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-03 00:35:23,475 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 00:35:23,475 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 01:28:56,125 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 01:28:56,125 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 11:51:00,138 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 11:51:00,138 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:02:09,409 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:02:09,409 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:11:32,137 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:11:32,137 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:15:10,506 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:15:10,506 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:17:52,486 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:17:52,486 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:40:12,298 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:40:12,298 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 12:47:13,533 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 12:47:13,533 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 13:03:59,965 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 13:03:59,965 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 14:42:46,833 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 14:42:46,833 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 15:47:19,622 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 15:47:19,622 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 15:49:51,255 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 15:49:51,256 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 16:57:30,305 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 16:57:30,305 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 17:33:55,195 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 17:33:55,196 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 17:47:20,707 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 17:47:20,707 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 17:47:36,725 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 17:47:36,726 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 17:48:16,523 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: admin, IP: 127.0.0.1, 原因: 用户不存在
2025-08-03 17:49:29,659 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 密码错误
2025-08-03 17:49:35,403 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: bearyang, IP: 127.0.0.1, 原因: 密码错误
2025-08-03 17:50:51,932 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin2, IP: 127.0.0.1
2025-08-03 17:50:52,361 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: HiXR0aoB..., 用户ID: 817187, 过期时间: 300秒, 结果: 成功
2025-08-03 17:51:06,251 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817187, token=HiXR0aoB...
2025-08-03 17:51:06,579 - modules.auth.services.auth_service - WARNING - auth_service.py:339 - _log_failed_login - 登录失败: admin2, IP: 127.0.0.1, 原因: 2FA验证失败
2025-08-03 17:52:16,435 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin2, IP: 127.0.0.1
2025-08-03 17:52:16,851 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: MJolIDkM..., 用户ID: 817187, 过期时间: 300秒, 结果: 成功
2025-08-03 17:52:27,869 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 17:52:27,869 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 17:52:46,598 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin2, IP: 127.0.0.1
2025-08-03 17:52:47,029 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: Ai5SdeJ_..., 用户ID: 817187, 过期时间: 300秒, 结果: 成功
2025-08-03 17:52:54,255 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: viewer1, IP: 127.0.0.1
2025-08-03 17:52:54,661 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: hI-2RrOk..., 用户ID: 817185, 过期时间: 300秒, 结果: 成功
2025-08-03 17:54:27,592 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: viewer1, IP: 127.0.0.1
2025-08-03 17:54:27,870 - modules.auth.api.auth_api - WARNING - auth_api.py:94 - login - 用户 viewer1 未启用2FA但系统未强制要求
2025-08-03 17:54:40,734 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: admin2, IP: 127.0.0.1
2025-08-03 17:54:41,161 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: DFPIj79q..., 用户ID: 817187, 过期时间: 300秒, 结果: 成功
2025-08-03 19:44:06,601 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 19:44:06,601 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:03:57,552 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:03:57,552 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:04:14,608 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:04:14,608 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:06:05,002 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:06:05,002 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:09:42,530 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:09:42,530 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:12:07,017 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:12:07,017 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 20:39:09,811 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 20:39:09,811 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 22:30:29,966 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 22:30:29,967 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 22:45:00,483 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 22:45:00,483 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 23:50:36,815 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-03 23:50:36,815 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-03 23:50:57,158 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-03 23:50:57,429 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: M3CvI6wJ..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-03 23:50:57,429 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-03 23:51:36,306 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=M3CvI6wJ...
2025-08-03 23:51:37,031 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-03 23:51:37,032 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: M3CvI6wJ...
2025-08-03 23:51:37,374 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=LVthRe6LbUWf5bHLn8W_ef48CR5iL36i6JWD4kdu-B8
2025-08-03 23:51:37,374 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-04 00:41:23,142 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-04 00:41:23,142 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-04 01:02:31,792 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-04 01:02:31,921 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-04 01:07:22,515 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-04 01:07:22,515 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-04 01:09:02,096 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-04 01:09:02,096 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-04 15:14:28,861 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-04 15:14:29,020 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-05 09:09:00,271 - modules.auth.services.session_manager - ERROR - session_manager.py:71 - validate_session - 验证会话失败: TransactionContext Error: Conflict on update!
2025-08-05 09:09:03,208 - modules.auth.services.auth_service - INFO - auth_service.py:53 - authenticate_user - 用户登录成功: bearyang, IP: 127.0.0.1
2025-08-05 09:09:03,552 - modules.auth - INFO - logging_config.py:137 - log_token_event - Token事件: token_generated, Token: Ex5aJUmq..., 用户ID: 817184, 过期时间: 300秒, 结果: 成功
2025-08-05 09:09:03,552 - modules.auth.api.auth_api - INFO - auth_api.py:205 - login - 为用户 bearyang 生成2FA临时token
2025-08-05 09:09:37,877 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:123 - verify_temp_token - 临时token验证成功: user_id=817184, token=Ex5aJUmq...
2025-08-05 09:09:38,707 - modules.auth.services.auth_service - INFO - auth_service.py:83 - verify_2fa_and_complete_login - 用户2FA验证成功: bearyang, IP: 127.0.0.1
2025-08-05 09:09:38,712 - modules.auth.services.temp_token_service - INFO - temp_token_service.py:148 - invalidate_temp_token - 临时token已失效: Ex5aJUmq...
2025-08-05 09:09:39,072 - modules.auth.services.session_manager - INFO - session_manager.py:34 - create_session - 创建会话: user_id=817184, session_id=Ns8yh1sNy4M3SVnHXTOqylfx58waB4jPZrGe2RDa84s
2025-08-05 09:09:39,072 - modules.auth.api.twofa_api - INFO - twofa_api.py:79 - verify_2fa - 用户2FA验证成功并完成登录: bearyang
2025-08-05 09:21:56,111 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-05 09:21:56,111 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-05 10:50:14,740 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-05 10:50:14,740 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-05 14:26:08,283 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-05 14:26:08,283 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-05 18:00:02,498 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-05 18:00:02,498 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-05 18:57:32,635 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-05 18:57:32,635 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
2025-08-05 19:40:50,165 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:51 - _cleanup_loop - Token清理循环开始，间隔: 300秒
2025-08-05 19:40:50,165 - modules.auth.services.token_cleanup_service - INFO - token_cleanup_service.py:36 - start_cleanup_service - Token清理服务已启动
